{"common": {"save": "Save", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "add": "Add", "close": "Close", "saving": "Saving...", "loading": "Loading...", "refresh": "Refresh", "continue": "Continue", "words": "words", "initError": "Initialization failed", "dragToReorder": "Drag to reorder", "deleteShortcut": "Delete (Ctrl+D)", "deleteWithShortcut": "Ctrl + 单击快速删除", "retry": "Retry"}, "homepage": {"myNovels": "My Novels", "createNewNovel": "Create New Novel", "noNovelsYet": "No novels yet. Click \"Create New Novel\" to start writing!", "searchPlaceholder": "Search novels...", "editNovel": "Edit Novel", "deleteNovel": "Delete Novel", "openNovel": "Open Novel", "exportNovel": "Export Novel", "exportSuccess": "Novel exported successfully", "exportError": "Export failed: {error}"}, "editor": {"backToHomepage": "← Back to Homepage", "chapterTitlePlaceholder": "Chapter Title", "autoSaveIndicator": "Auto-saved", "manualSaveSuccess": "Manual save successful", "wordCount": "{count}", "unsavedChanges": "Unsaved", "startWriting": "Start writing...", "rewriteTooltip": {"title": "Text Rewrite", "expand": "Expand", "contract": "Contract", "beautify": "Beautify", "custom": "Custom", "customPromptPlaceholder": "Enter your custom prompt...", "includeFullContext": "Include full book context", "includeFullContextDesc": "Include full book summary and world book information for better context", "apply": "Apply", "cancel": "Cancel", "processing": "Processing...", "error": "Rewrite failed: {error}", "noModelConfigured": "Please configure rewrite model in settings first"}, "contextMenu": {"copy": "Copy", "cut": "Cut", "paste": "Paste", "aiRewrite": "AI Rewrite", "customRewrite": "Custom Rewrite..."}, "sidebar": {"chaptersList": "Chapters List", "collapseSidebar": "Collapse Sidebar", "expandChaptersList": "Expand Chapters List", "addNewChapter": "+ Add New Chapter"}, "aiPanel": {"title": "AI Tools", "collapsePanel": "Collapse Sidebar", "expandPanel": "Expand AI Tools", "originalText": "Original Text", "rewriteResult": "Rewrite Result", "generating": "Generating", "replace": "Replace", "retry": "Retry", "furtherRequest": "Further Request", "furtherPromptPlaceholder": "Enter further requirements or modification suggestions...", "applyFurther": "Apply", "welcomeTitle": "AI Writing Assistant", "welcomeDescription": "Select text and right-click to use AI rewrite features", "tips": "Usage Tips", "tip1": "Right-click selected text to see rewrite options", "tip2": "Supports expand, contract, beautify and custom rewrites", "tip3": "Can further optimize based on rewrite results", "placeholderTitle": "AI Writing Assistant", "placeholderDescription": "Intelligent writing features coming soon", "status": "In Development...", "features": {"continueWriting": "Smart Continue", "plotSuggestions": "Plot Suggestions", "textOptimization": "Text Optimization", "contentAnalysis": "Content Analysis"}, "featureDescriptions": {"continueWriting": "Automatically continue writing based on your existing content", "plotSuggestions": "Get suggestions for plot development and story direction", "textOptimization": "Improve your text with grammar and style enhancements", "contentAnalysis": "Analyze your content for themes, characters, and story structure"}}, "continueWriting": {"title": "AI Continue Writing", "tooltip": "Continue writing with AI", "processing": "Processing...", "result": "Continue Writing Result", "append": "Append Text"}}, "modals": {"newNovel": {"title": "Create New Novel", "nameLabel": "Novel Name", "namePlaceholder": "Enter novel name", "authorLabel": "Author", "authorPlaceholder": "Enter author name", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter novel description"}, "editNovel": {"title": "Edit Novel", "nameLabel": "Novel Name", "namePlaceholder": "Enter novel name", "authorLabel": "Author", "authorPlaceholder": "Enter author name", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter novel description"}}, "chapters": {"chapter": "Chapter(s)", "untitled": "Untitled Chapter", "deleteConfirmation": "Are you sure you want to delete this chapter? This action cannot be undone.", "atLeastOneChapter": "At least one chapter must be retained", "newChapterTitle": "Chapter {number}", "created": "Chapter created", "deleted": "Chapter deleted", "titleUpdated": "Title updated", "reordered": "Chapter order updated", "deleteWithShortcut": "Delete chapter (Ctrl+click to delete directly)"}, "oobe": {"next": "Next", "previous": "Previous", "finish": "Finish", "page1": {"subtitle": "AI-powered novel writing tool", "language": "Language Selection"}, "page2": {"title": "Cross-platform Support", "description": "Built with modern technologies: Electron + Vue.js"}, "page3": {"title": "Free and Open Source", "description": "Configure your own model providers", "feature1": "Fully open source and customizable", "feature2": "Supports multiple AI model providers"}}, "settings": {"title": "Settings", "backToHome": "Back to Home", "language": {"title": "Language", "interface": "Interface Language"}, "appearance": {"title": "Appearance", "theme": "Theme", "themes": {"light": "Light", "dark": "Dark", "oled": "OLED Black", "blue": "Tech Blue", "green": "Fresh Green", "purple": "Elegant Purple"}}, "storage": {"title": "Storage", "path": "Data Storage Path", "change": "Change", "reset": "Reset to De<PERSON>ult", "loading": "Loading...", "failed": "Failed to get path", "selectFailed": "Failed to select storage directory", "resetFailed": "Failed to reset storage directory"}, "aiFeatures": {"title": "AI Features", "rewriteModel": "Text Rewrite Model", "continueModel": "Continue Writing Model", "summaryModel": "Summary Model", "selectProvider": "Select Provider", "selectModel": "Select Model", "noProvidersConfigured": "No providers configured. Please configure them in Provider Settings first.", "rewriteOptions": {"expand": "Expand", "contract": "Contract", "beautify": "Beautify", "custom": "Custom Prompt"}, "systemPrompt": "System Prompt", "systemPromptSaved": "System prompt saved successfully"}, "provider": {"title": "Provider <PERSON>s", "listTitle": "Provider List", "add": "Add Provider", "name": "Name", "type": "Type", "baseUrl": "Base URL", "apiKey": "API Key", "models": "Available Models", "noModels": "No models available. Please enter an API key to fetch model list", "selectProvider": "Please select a provider from the left", "untitled": "Untitled Provider", "namePlaceholder": "Enter provider name", "baseUrlPlaceholder": "e.g. https://api.openai.com/v1", "apiKeyPlaceholder": "Enter API key", "nameRequired": "Please enter a provider name", "deleteConfirm": "Are you sure you want to delete this provider?", "saved": "Provider saved", "fetchModels": "Fetch Models", "fetchModelsFailed": "Failed to fetch model list", "modelName": "Model name", "addModelPlaceholder": "Enter model name to add"}, "developer": {"title": "Developer Settings", "resetoobe": "Reset OOBE"}}, "about": {"title": "About", "appName": "NovelBox", "appDescription": "Unleash unlimited creativity with LLM!", "version": "Version {version}", "github": "<PERSON><PERSON><PERSON>", "contact": "Contact Me", "license": "License"}, "wordCount": {"count": "{count} words", "kilo": "{count}k words", "million": "{count}m words"}, "aiPanel": {"tabs": {"context": "Context Manager", "worldbook": "World Book", "settings": "AI Settings", "tips": "Usage Tips"}, "settings": {"title": "AI Settings", "autoSummary": "Auto Summary", "autoSummaryDesc": "Automatically generate chapter summaries after editing", "includeFullContext": "Include Full Context by <PERSON><PERSON><PERSON>", "includeFullContextDesc": "Include full book summary when rewriting text", "alwaysIncludeWorldBook": "Always Include World Book", "alwaysIncludeWorldBookDesc": "Always include world book information in AI requests"}, "tips": {"title": "Usage Tips", "contextTitle": "Context Manager", "contextDesc": "Automatically generate chapter summaries to help AI understand your story better", "worldbookTitle": "World Book", "worldbookDesc": "Define characters, locations, and world settings for consistent storytelling", "rewriteTitle": "Text Rewriting", "rewriteDesc": "Select text and right-click to access AI rewriting options", "selectionTitle": "Smart Selection", "selectionDesc": "Select text up to 1000 characters for optimal rewriting results"}}, "contextManager": {"title": "Context Manager", "completed": "Completed", "processing": "Processing", "failed": "Failed", "pending": "Pending", "generateAll": "Generate All Summaries", "generateFullBook": "Generate Full Book Summary", "chaptersList": "Chapters List", "autoSummary": "Auto Summary", "view": "View", "generate": "Generate", "generating": "Generating...", "summary": "Summary", "lastUpdated": "Last Updated", "regenerate": "Regenerate", "statusCompleted": "Completed", "statusProcessing": "Processing", "statusFailed": "Failed", "statusPending": "Pending", "refreshError": "Failed to refresh progress", "generateError": "Failed to generate summary", "batchGenerateComplete": "Batch generation completed", "batchGenerateError": "Batch generation failed", "fullBookGenerateError": "Failed to generate full book summary", "fullBookSummary": "Full Book Summary"}, "worldBook": {"title": "World Book", "world": "World", "characters": "Characters", "locations": "Locations", "items": "Items", "timeline": "Timeline", "worldName": "World Name", "worldNamePlaceholder": "Enter world name...", "worldDescription": "World Description", "worldDescriptionPlaceholder": "Describe your world...", "worldRules": "World Rules", "rulePlaceholder": "Enter a rule...", "addRule": "Add Rule", "addCharacter": "Add Character", "noCharactersYet": "No characters added yet", "addFirstCharacter": "Add First Character", "characterName": "Character Name", "description": "Description", "characterDescriptionPlaceholder": "Describe this character...", "example": "Description Example", "examplePlaceholder": "Write a short example of character description (appearance, speech, etc.)...", "traits": "Trai<PERSON>", "addTrait": "Add trait...", "addLocation": "Add Location", "locationName": "Location Name", "locationDescriptionPlaceholder": "Describe this location...", "significance": "Significance", "significancePlaceholder": "Why is this important?", "addItem": "Add Item", "itemName": "Item Name", "itemDescriptionPlaceholder": "Describe this item...", "addEvent": "Add Event", "eventTitle": "Event Title", "eventDate": "Date/Time", "eventDatePlaceholder": "When did this happen?", "eventDescriptionPlaceholder": "Describe this event...", "saveSuccess": "World book saved successfully", "saveError": "Failed to save world book", "loadError": "Failed to load world book"}, "migration": {"title": "Data Migration", "whyMigrate": "Why migrate?", "whyMigrateDesc": "We've improved the storage system to better support large novels and new features. Your data needs to be migrated to the new format.", "whatWillHappen": "What will happen?", "step1": "Your existing novels will be converted to the new storage format", "step2": "Chapter summaries and AI contexts will be initialized", "step3": "World books will be created for each novel", "step4": "AI configurations will be set to defaults", "important": "Important", "importantDesc": "This process is safe and your original data will be preserved. The migration may take a few minutes for large novels.", "startMigration": "Start Migration", "inProgress": "Migration in Progress", "log": "Migration Log", "success": "Migration Successful", "failed": "Migration Failed", "successDesc": "Successfully migrated {count} out of {total} novels.", "failedDesc": "Migration failed. Please try again or contact support.", "validation": "Validation", "retry": "Retry", "starting": "Starting migration...", "novelSuccess": "Successfully migrated: {title}", "novelError": "Failed to migrate {title}: {error}", "completed": "Migration completed", "validating": "Validating migration results...", "validationSuccess": "Validation successful", "validationFailed": "Validation failed", "error": "Migration error: {error}", "migrationCompleteMessage": "Data migration completed successfully"}, "toast": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Info"}}